<!--<template>-->
<!--  <DiCustomModal-->
<!--    ref="modal"-->
<!--    title="Connect Your DataSource"-->
<!--    subtitle="To use the template, connect your corresponding datasource for each of the suggested data shown, you might need the following connectors:"-->
<!--    hide-header-close-->
<!--    size="lg"-->
<!--    button-size="small"-->
<!--    @hidden="reset"-->
<!--    @onClickOk="handleSubmitConnectDataSource"-->
<!--    @onCancel="handleOnCancel"-->
<!--    ignore-enforce-focus-selector=".job-creation-modal-body"-->
<!--  >-->
<!--    <div class="connect-datasource-body">-->
<!--      <label class="connect-datasource-body&#45;&#45;label">DataSource: </label>-->
<!--      <template v-for="dataSource in requiredDataSourceList">-->
<!--        <div :key="dataSource.name" class="connect-datasource-item">-->
<!--          <div class="connect-datasource-item&#45;&#45;left">-->
<!--            <img :src="dataSource.iconSrc" alt="" class="connect-datasource-item&#45;&#45;left&#45;&#45;img" />-->
<!--            <span class="connect-datasource-item&#45;&#45;left&#45;&#45;name">{{ dataSource.name }}</span>-->
<!--          </div>-->
<!--          <div class="connect-datasource-item&#45;&#45;right" v-b-hover="isHover => $set(dataSource, 'isHover', isHover)">-->
<!--            <DiButton v-if="dataSource.isConnected && !dataSource.isHover" title="Connected" class="connect-datasource-item&#45;&#45;right&#45;&#45;btn" disabled>-->
<!--              <i class="di-icon-check"></i>-->
<!--            </DiButton>-->
<!--            <DiButton-->
<!--              v-else-if="dataSource.isConnected && dataSource.isHover"-->
<!--              :is-loading="dataSource.isConnecting"-->
<!--              title="Re-connect"-->
<!--              class="connect-datasource-item&#45;&#45;right&#45;&#45;btn"-->
<!--              @click="handleSetupDataSource(dataSource)"-->
<!--            >-->
<!--              <i v-if="!dataSource.isConnecting" class="di-icon-refresh"></i>-->
<!--            </DiButton>-->
<!--            <DiButton-->
<!--              v-else-->
<!--              title="Connect"-->
<!--              :is-loading="dataSource.isConnecting"-->
<!--              class="connect-datasource-item&#45;&#45;right&#45;&#45;btn"-->
<!--              text-accent-->
<!--              @click="handleSetupDataSource(dataSource)"-->
<!--            >-->
<!--              <i class="di-icon-connect" v-if="!dataSource.isConnecting"></i>-->
<!--            </DiButton>-->
<!--          </div>-->
<!--        </div>-->
<!--      </template>-->
<!--&lt;!&ndash;      <MultiJobCreationModal :is-disabled-select-source="true" :showAtBody="true" ref="multiJobCreationModal"></MultiJobCreationModal>&ndash;&gt;-->
<!--&lt;!&ndash;      <DataSourceConfigModal&ndash;&gt;-->
<!--&lt;!&ndash;        ref="dataSourceConfigModal"&ndash;&gt;-->
<!--&lt;!&ndash;        :data-source-render.sync="dataSourceFormRender"&ndash;&gt;-->
<!--&lt;!&ndash;        :isShow.sync="isShowDataSourceConfigModal"&ndash;&gt;-->
<!--&lt;!&ndash;        @onClickOk="handleSubmitDataSource"&ndash;&gt;-->
<!--&lt;!&ndash;        @reset="resetDataSourceFormRender"&ndash;&gt;-->
<!--&lt;!&ndash;      ></DataSourceConfigModal>&ndash;&gt;-->
<!--    </div>-->
<!--  </DiCustomModal>-->
<!--</template>-->

<!--<script lang="ts">-->
<!--import DataSourceConfigModal from '@/screens/data-ingestion/components/DataSourceConfigModal.vue';-->
<!--import { SchedulerDaily } from '@/screens/data-ingestion/components/job-scheduler-form/scheduler-time';-->
<!--import MultiJobCreationModal from '@/screens/data-ingestion/components/MultiJobCreationModal.vue';-->
<!--import { DataSourceFormFactory } from '@/screens/data-ingestion/form-builder/DataSourceFormFactory';-->
<!--import { DataSourceModule } from '@/screens/data-ingestion/store/DataSourceStore';-->
<!--import { JobModule } from '@/screens/data-ingestion/store/JobStore';-->
<!--import DiCustomModal from '@/shared/components/DiCustomModal.vue';-->
<!--import { ListUtils, PopupUtils, TimeoutUtils } from '@/utils';-->
<!--import { DIException, TemplateInfo } from '@core/common/domain';-->
<!--import { AtomicAction } from '@core/common/misc';-->
<!--import { Job } from '@core/data-ingestion';-->
<!--import { DataSourceInfo } from '@core/data-ingestion/domain/data-source/DataSourceInfo';-->
<!--import { DataSourceType } from '@core/data-ingestion/domain/data-source/DataSourceType';-->
<!--import { Component, Ref, Vue, Watch } from 'vue-property-decorator';-->
<!--import cloneDeep from 'lodash/cloneDeep';-->
<!--import { Log } from '@core/utils';-->

<!--interface RequiredDataSource {-->
<!--  type: DataSourceType;-->
<!--  name: string;-->
<!--  iconSrc: any;-->
<!--  isConnected: boolean;-->
<!--  isConnecting?: boolean;-->
<!--  isHover?: boolean;-->
<!--}-->

<!--@Component({-->
<!--  components: {-->
<!--    DiCustomModal,-->
<!--    // DataSourceConfigModal,-->
<!--    // MultiJobCreationModal-->
<!--  }-->
<!--})-->
<!--export default class ConnectDataSourceModal extends Vue {-->
<!--  protected requiredDataSourceList: RequiredDataSource[] = [];-->
<!--  private onApply?: (isChanged: boolean) => Promise<void>;-->
<!--  private onCancel?: () => Promise<void>;-->
<!--  private onTemplateInfoChanged?: (templateInfo: TemplateInfo) => Promise<void>;-->
<!--  private selectedSource: RequiredDataSource | null = null;-->
<!--  private templateInfo: TemplateInfo = TemplateInfo.createDefault();-->
<!--  private isDataChanged = false;-->

<!--  private isShowDataSourceConfigModal = false;-->
<!--  private dataSourceFormRender = new DataSourceFormFactory().createRender(DataSourceInfo.createDefault(DataSourceType.Shopify), this.handleSubmitDataSource);-->

<!--  protected resetDataSourceFormRender() {-->
<!--    this.dataSourceFormRender = new DataSourceFormFactory().createRender(DataSourceInfo.createDefault(DataSourceType.Shopify), this.handleSubmitDataSource);-->
<!--  }-->

<!--  @Ref()-->
<!--  private dataSourceConfigModal!: DataSourceConfigModal;-->

<!--  @Ref()-->
<!--  protected readonly modal!: DiCustomModal;-->

<!--  @Ref()-->
<!--  protected readonly multiJobCreationModal!: MultiJobCreationModal;-->

<!--  private show(): void {-->
<!--    this.modal.show();-->
<!--  }-->

<!--  public requireConnectDataSource(data: {-->
<!--    templateInfo?: TemplateInfo;-->
<!--    onApply?: (isChanged: boolean) => Promise<void>;-->
<!--    onAlreadyApplied?: () => Promise<void>;-->
<!--    onCancel?: () => Promise<void>;-->
<!--    onTemplateInfoChanged?: (templateInfo: TemplateInfo) => Promise<void>;-->
<!--    forceEdit?: boolean;-->
<!--  }) {-->
<!--    const { templateInfo, onCancel, onApply: onSuccess, onTemplateInfoChanged, forceEdit, onAlreadyApplied } = data;-->
<!--    this.onApply = onSuccess;-->
<!--    this.onCancel = onCancel;-->
<!--    this.onTemplateInfoChanged = onTemplateInfoChanged;-->
<!--    this.isDataChanged = false;-->

<!--    if (templateInfo && this.isShowSetupConnect(templateInfo, forceEdit)) {-->
<!--      this.processRequireConnectionDataSource(cloneDeep(templateInfo));-->
<!--      this.show();-->
<!--    } else {-->
<!--      onAlreadyApplied?.apply(this);-->
<!--    }-->
<!--  }-->

<!--  protected isShowSetupConnect(templateInfo: TemplateInfo, forceEdit?: boolean): boolean {-->
<!--    if (forceEdit) {-->
<!--      return true;-->
<!--    }-->
<!--    return this.isRequireSetupConnect(templateInfo);-->
<!--  }-->

<!--  private reset(): void {-->
<!--    this.onApply = void 0;-->
<!--    this.onCancel = void 0;-->
<!--    this.onTemplateInfoChanged = void 0;-->
<!--    this.selectedSource = null;-->
<!--    this.isDataChanged = false;-->
<!--    this.templateInfo = TemplateInfo.createDefault();-->
<!--    this.requiredDataSourceList = [];-->
<!--    this.isShowDataSourceConfigModal = false;-->
<!--    this.resetDataSourceFormRender();-->
<!--  }-->

<!--  private isRequireSetupConnect(templateInfo: TemplateInfo): boolean {-->
<!--    return ListUtils.isNotEmpty(templateInfo.setting.getNotConnectedDataSourceList());-->
<!--  }-->

<!--  protected async handleSetupDataSource(source: RequiredDataSource): Promise<void> {-->
<!--    try {-->
<!--      this.$set(source, 'isConnecting', true);-->
<!--      this.selectedSource = source;-->
<!--      await TimeoutUtils.sleepRandom(10, 20);-->
<!--      this.dataSourceFormRender = new DataSourceFormFactory().createRender(DataSourceInfo.createDefault(source.type), this.handleSubmitDataSource);-->
<!--      this.isShowDataSourceConfigModal = true;-->
<!--    } catch (error) {-->
<!--      this.selectedSource = null;-->
<!--      this.$set(source, 'isConnecting', false);-->
<!--      const ex = DIException.fromObject(error);-->
<!--      PopupUtils.showError(ex.getPrettyMessage());-->
<!--    }-->
<!--  }-->

<!--  @Watch('isShowDataSourceConfigModal')-->
<!--  private onIsShowDataSourceConfigModalChanged(isShow: boolean): void {-->
<!--    if (!isShow && this.selectedSource) {-->
<!--      this.$set(this.selectedSource, 'isConnecting', false);-->
<!--    }-->
<!--  }-->

<!--  @AtomicAction()-->
<!--  private async handleSubmitDataSource(): Promise<void> {-->
<!--    try {-->
<!--      this.dataSourceConfigModal.setSubmitLoading(true);-->
<!--      this.dataSourceConfigModal.setErrorMsg('');-->
<!--      const sourceInfo: DataSourceInfo = this.dataSourceFormRender.createDataSourceInfo();-->
<!--      const createdSource = await DataSourceModule.createDataSource(sourceInfo);-->
<!--      this.dataSourceConfigModal.hide();-->
<!--      await this.handleAfterCreateSource(createdSource);-->
<!--    } catch (error) {-->
<!--      const ex = DIException.fromObject(error);-->
<!--      this.dataSourceConfigModal.setErrorMsg(ex.getPrettyMessage());-->
<!--    } finally {-->
<!--      this.dataSourceConfigModal.setSubmitLoading(false);-->
<!--    }-->
<!--  }-->

<!--  private async handleAfterCreateSource(createdSource: DataSourceInfo): Promise<void> {-->
<!--    const job: Job = Job.default(createdSource);-->
<!--    job.scheduleTime = new SchedulerDaily(1, Date.now());-->
<!--    job.withDisplayName(`${createdSource.getDisplayName()} job`);-->
<!--    this.multiJobCreationModal.show(-->
<!--      job,-->
<!--      async (job: Job) => {-->
<!--        Log.debug('handleAfterCreateSource::job::dbName', job.destDatabaseName);-->
<!--        Log.debug('handleAfterCreateSource::job::table', job.destDatabaseName);-->
<!--        const jobs: Job[] = Job.getMultiJob(job);-->
<!--        Log.debug('handleAfterCreateSource::job[0]::dbName', jobs[0].destDatabaseName);-->
<!--        Log.debug('handleAfterCreateSource::job[0]::table', jobs[0].destDatabaseName);-->
<!--        const newJobs: Job[] = await JobModule.multiCreateV2({ jobs: jobs, isRunNow: true });-->
<!--        this.completeSetupDataSource(createdSource, newJobs, job.destDatabaseName);-->
<!--      },-->
<!--      this.multiJobCreationModal.hide,-->
<!--      false-->
<!--    );-->
<!--    this.multiJobCreationModal.handleSelectDataSource(createdSource);-->
<!--  }-->

<!--  private async completeSetupDataSource(createdSource: DataSourceInfo, createdJobs: Job[], destDatabaseName: string): Promise<void> {-->
<!--    if (!this.selectedSource) {-->
<!--      PopupUtils.showError('Something went wrong, please try again later.');-->
<!--    }-->
<!--    const currentSource = this.selectedSource!;-->
<!--    try {-->
<!--      this.$set(currentSource, 'isConnecting', true);-->
<!--      const jobIds = createdJobs.map(job => job.jobId);-->
<!--      this.templateInfo.setting.setConnected(currentSource.type, createdSource.id, jobIds, destDatabaseName);-->
<!--      this.$set(currentSource, 'isConnected', true);-->
<!--      this.applyTemplateInfoChanged();-->
<!--    } finally {-->
<!--      this.$set(currentSource, 'isConnecting', false);-->
<!--    }-->
<!--  }-->

<!--  private applyTemplateInfoChanged(): void {-->
<!--    this.isDataChanged = true;-->
<!--    if (this.onTemplateInfoChanged) {-->
<!--      this.onTemplateInfoChanged(this.templateInfo);-->
<!--    }-->
<!--  }-->

<!--  private hide(): void {-->
<!--    this.modal.hide();-->
<!--  }-->

<!--  @AtomicAction()-->
<!--  private async handleSubmitConnectDataSource(): Promise<void> {-->
<!--    try {-->
<!--      this.modal.setLoading(true);-->
<!--      this.modal.setError(null);-->
<!--      if (this.isRequireSetupConnect(this.templateInfo)) {-->
<!--        throw new Error('Please connect all required data source.');-->
<!--      }-->
<!--      if (this.onApply) {-->
<!--        await this.onApply(this.isDataChanged);-->
<!--      }-->
<!--      this.hide();-->
<!--    } catch (error) {-->
<!--      const ex = DIException.fromObject(error);-->
<!--      this.modal.setError(ex.getPrettyMessage());-->
<!--    } finally {-->
<!--      this.modal.setLoading(false);-->
<!--    }-->
<!--  }-->

<!--  protected handleOnCancel(): void {-->
<!--    Log.debug('handleOnCancel');-->
<!--    if (this.onCancel) {-->
<!--      this.onCancel();-->
<!--    }-->
<!--    // this.hide();-->
<!--  }-->

<!--  private processRequireConnectionDataSource(templateInfo: TemplateInfo): void {-->
<!--    this.templateInfo = templateInfo;-->
<!--    this.requiredDataSourceList = templateInfo.setting.requiredDatasourceList.map(requiredDataSource => {-->
<!--      switch (requiredDataSource.type) {-->
<!--        case DataSourceType.Shopify:-->
<!--          return {-->
<!--            type: DataSourceType.Shopify,-->
<!--            name: 'Shopify',-->
<!--            iconSrc: require('@/assets/icon/data_ingestion/datasource/ic_shopify_small.png'),-->
<!--            isConnected: requiredDataSource.isConnected-->
<!--          };-->
<!--        case DataSourceType.GA:-->
<!--        case DataSourceType.GA4:-->
<!--          return {-->
<!--            type: requiredDataSource.type,-->
<!--            name: 'Google Analytics',-->
<!--            iconSrc: require('@/assets/icon/data_ingestion/datasource/ic_ga_4_small.svg'),-->
<!--            isConnected: requiredDataSource.isConnected-->
<!--          };-->
<!--        default:-->
<!--          return {-->
<!--            type: requiredDataSource.type,-->
<!--            name: 'Unknown',-->
<!--            iconSrc: require('@/assets/icon/data_ingestion/status/unknown.svg'),-->
<!--            isConnected: requiredDataSource.isConnected-->
<!--          };-->
<!--      }-->
<!--    });-->
<!--  }-->
<!--}-->
<!--</script>-->

<!--<style lang="scss">-->
<!--.connect-datasource-body {-->
<!--  &&#45;&#45;label {-->
<!--    font-size: 16px;-->
<!--    font-weight: 500;-->
<!--    line-height: 19px;-->
<!--    letter-spacing: 0.23px;-->
<!--    text-align: left;-->
<!--  }-->

<!--  .connect-datasource-item {-->
<!--    height: 46px;-->
<!--    display: flex;-->
<!--    justify-content: space-between;-->
<!--    align-items: center;-->

<!--    &&#45;&#45;left {-->
<!--      &&#45;&#45;img {-->
<!--        width: 24px;-->
<!--        height: 24px;-->
<!--        margin-right: 12px;-->
<!--      }-->

<!--      &&#45;&#45;name {-->
<!--        font-size: 14px;-->
<!--        font-weight: 500;-->
<!--        line-height: 16px;-->
<!--        letter-spacing: 0em;-->
<!--        text-align: left;-->

<!--        color: var(&#45;&#45;text-color);-->
<!--      }-->
<!--    }-->
<!--  }-->
<!--}-->
<!--</style>-->
