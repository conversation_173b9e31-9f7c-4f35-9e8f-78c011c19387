<script lang="ts">
import { Component, Prop } from 'vue-property-decorator';
import SplitPanelMixin from '@/shared/components/layout-wrapper/SplitPanelMixin';
import AIChartPreviewPanel from '@/screens/dashboard-detail/components/ai-builder-modal/panel/preview-panel/AIChartPreviewPanel.vue';
import AIChatPanel from '@/screens/dashboard-detail/components/ai-builder-modal/panel/AIChatPanel.vue';
import { AIBuilderController } from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilderController';

@Component({
  components: { AIChatPanel, AIChartPreviewPanel }
})
export default class AIBuilder extends SplitPanelMixin {
  @Prop({ required: true })
  protected controller!: AIBuilderController;

  protected get panelSize() {
    return this.getPanelSizeHorizontal();
  }

  protected resizeChart() {
    //
  }
}
</script>

<template>
  <Split :gutterSize="1" class="d-flex" id="ai-builder-container" @onDragEnd="resizeChart">
    <SplitArea :size="panelSize[0]" :minSize="0">
      <AIChatPanel :controller="controller" />
    </SplitArea>

    <SplitArea :size="panelSize[1]" :minSize="0">
      <AIChartPreviewPanel :controller="controller" />
    </SplitArea>
  </Split>
</template>

<style lang="scss">
#ai-builder-container {
  .gutter-horizontal:before {
    display: none;
  }
}
</style>
