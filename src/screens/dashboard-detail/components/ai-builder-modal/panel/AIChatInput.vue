<script lang="ts">
import { Vue, Component, Prop, Ref, Watch } from 'vue-property-decorator';
import { min } from 'lodash';

@Component({})
export default class AIChatInput extends Vue {
  @Prop() inputPlaceholder!: string;
  @Prop() inputDisablePlaceholder!: string;
  @Prop({ default: false }) inputDisable!: boolean;

  @Ref()
  private readonly diMessageInput?: HTMLTextAreaElement;

  messageText: string | null = null;
  private height = '46px';
  private isComposing = false;

  get actionClass(): string[] {
    const actionClasses: string[] = [];

    if (this.inputDisable) {
      actionClasses.push('di-board-action--disabled');
    }

    if (this.messageText) {
      actionClasses.push('di-board-action--typing');
    }

    // TODO: sending

    return actionClasses;
  }

  get actionStyle(): any {
    return { height: this.height };
  }

  @Watch('messageText')
  onMessageChanged() {
    if (this.messageText === null || this.messageText?.trim()?.length === 0) {
      this.height = '46px';
      return;
    }
    const heightTextArea = this.diMessageInput?.scrollHeight === 0 ? 30 : this.diMessageInput!.scrollHeight;
    this.height = `${min([16 + heightTextArea, 200])}px`;
  }

  private isSending = false;

  sendMessage(event?: Event) {
    // Handle keyboard event
    if (event && event.type === 'keydown') {
      const keyEvent = event as KeyboardEvent;
      if (keyEvent.shiftKey) {
        return;
      }
      // Prevent sending message during IME composition
      if (this.isComposing) {
        return;
      }
      keyEvent.preventDefault();
    }

    // Prevent double sending
    if (this.isSending || (this.messageText?.trim()?.length ?? 0) === 0) {
      return;
    }

    this.isSending = true;
    this.$emit('msg-send', { text: this.messageText });
    this.messageText = null;

    // Reset flag after short delay
    setTimeout(() => {
      this.isSending = false;
    }, 100);
  }

  expandText(text: string) {
    if (this.messageText) {
      this.messageText.concat(text);
    } else {
      this.messageText = text;
    }
    this.$nextTick(() => {
      this.diMessageInput!.focus();
      this.diMessageInput!.scrollTop = this.diMessageInput!.scrollHeight;
      this.onMessageChanged();
    });
  }

  onCompositionStart() {
    this.isComposing = true;
  }

  onCompositionEnd() {
    this.isComposing = false;
  }
}
</script>

<template>
  <div class="di-board-action" :class="actionClass" :style="actionStyle">
    <div class="di-board-action__wrapper">
      <div class="di-board-action__msg-box">
        <textarea
          class="di-board-action__input"
          v-model="messageText"
          ref="diMessageInput"
          :disabled="inputDisable"
          :placeholder="inputPlaceholder"
          @keydown.enter="sendMessage"
          @compositionstart="onCompositionStart"
          @compositionend="onCompositionEnd"
        ></textarea>
        <div class="di-board-action__disable-text" v-if="inputDisablePlaceholder && inputDisable">
          <span>{{ inputDisablePlaceholder }}</span>
        </div>
      </div>
      <div class="di-board-action__extra">
        <button class="di-action-item di-action-item--send" @click="sendMessage">
          <slot name="sendButton">
            <i class="di-icon-arrow-up"></i>
          </slot>
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" src="@/screens/dashboard-detail/components/chatbot/chatbot.scss" />
