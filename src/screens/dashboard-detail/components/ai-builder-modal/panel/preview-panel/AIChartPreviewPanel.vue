<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import AIChartPreviewHeader from '@/screens/dashboard-detail/components/ai-builder-modal/panel/preview-panel/AIChartPreviewHeader.vue';
import AIChartPreviewBody from '@/screens/dashboard-detail/components/ai-builder-modal/panel/preview-panel/AIChartPreviewBody.vue';
import { AIBuilderController } from '@/screens/dashboard-detail/components/ai-builder-modal/AIBuilderController';

@Component({
  components: { AIChartPreviewBody, AIChartPreviewHeader }
})
export default class AIChartPreviewPanel extends Vue {
  @Prop({ required: true })
  protected controller!: AIBuilderController;
}
</script>

<template>
  <div class="ai-chart-preview-panel">
    <AIChartPreviewHeader />
    <AIChartPreviewBody :controller="controller" />
  </div>
</template>

<style scoped lang="scss">
.ai-chart-preview-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
</style>
